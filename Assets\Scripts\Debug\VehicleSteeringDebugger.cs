using UnityEngine;

/// <summary>
/// 车辆转向调试器 - 用于实时调试和测试转向系统
/// </summary>
[RequireComponent(typeof(CarController))]
public class VehicleSteeringDebugger : MonoBehaviour
{
    [Header("调试显示")]
    [Tooltip("是否显示转向调试信息")]
    public bool showDebugInfo = true;
    
    [Tooltip("是否在Scene视图中显示转向可视化")]
    public bool showSteeringVisualization = true;
    
    [Header("实时调试参数")]
    [Tooltip("实时调整转向平滑速度")]
    [Range(1f, 20f)]
    public float debugSteerSmoothingSpeed = 12f;
    
    [Tooltip("实时调整街机转向增强")]
    [Range(1f, 3f)]
    public float debugArcadeSteerBoost = 1.5f;
    
    [Tooltip("实时调整转向稳定性")]
    [Range(0f, 1f)]
    public float debugSteerStabilityFactor = 0.3f;

    private CarController m_CarController;
    private Rigidbody m_Rigidbody;
    
    // 调试信息
    private float m_CurrentSpeed;
    private float m_CurrentSteerInput;
    private float m_CurrentSteerAngle;
    private float m_SteerFactor;
    private float m_AngularVelocity;

    private void Awake()
    {
        m_CarController = GetComponent<CarController>();
        m_Rigidbody = GetComponent<Rigidbody>();
    }

    private void Update()
    {
        if (m_CarController == null || m_Rigidbody == null) return;

        // 实时更新调试参数
        m_CarController.steerSmoothingSpeed = debugSteerSmoothingSpeed;
        m_CarController.arcadeSteerBoost = debugArcadeSteerBoost;
        m_CarController.steerStabilityFactor = debugSteerStabilityFactor;

        // 收集调试信息
        m_CurrentSpeed = m_CarController.GetCurrentForwardSpeedMS() * 3.6f; // 转换为 km/h
        m_AngularVelocity = m_Rigidbody.angularVelocity.magnitude;
        
        // 获取当前转向输入和角度（通过反射或公共属性）
        UpdateSteeringDebugInfo();
    }

    private void UpdateSteeringDebugInfo()
    {
        // 这里可以通过反射获取私有字段，或者在CarController中添加公共属性
        // 为了简化，我们使用Input系统直接获取
        m_CurrentSteerInput = Input.GetAxis("Horizontal");
        
        // 估算当前转向角度
        if (m_CarController.wheelFL != null)
        {
            m_CurrentSteerAngle = m_CarController.wheelFL.steerAngle;
        }
    }

    private void OnGUI()
    {
        if (!showDebugInfo) return;

        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.BeginVertical("box");
        
        GUILayout.Label("车辆转向调试信息", EditorGUIUtility.isProSkin ? GUI.skin.label : GUI.skin.box);
        GUILayout.Space(5);
        
        GUILayout.Label($"当前速度: {m_CurrentSpeed:F1} km/h");
        GUILayout.Label($"转向输入: {m_CurrentSteerInput:F2}");
        GUILayout.Label($"转向角度: {m_CurrentSteerAngle:F1}°");
        GUILayout.Label($"角速度: {m_AngularVelocity:F2} rad/s");
        
        GUILayout.Space(10);
        GUILayout.Label("实时调试参数:", EditorGUIUtility.isProSkin ? GUI.skin.label : GUI.skin.box);
        GUILayout.Label($"转向平滑: {debugSteerSmoothingSpeed:F1}");
        GUILayout.Label($"街机增强: {debugArcadeSteerBoost:F1}");
        GUILayout.Label($"稳定性: {debugSteerStabilityFactor:F2}");
        
        GUILayout.EndVertical();
        GUILayout.EndArea();
    }

    private void OnDrawGizmos()
    {
        if (!showSteeringVisualization || m_Rigidbody == null) return;

        // 绘制车辆速度向量
        Gizmos.color = Color.green;
        Vector3 velocityDirection = m_Rigidbody.linearVelocity.normalized;
        Gizmos.DrawRay(transform.position, velocityDirection * 3f);
        
        // 绘制车辆朝向
        Gizmos.color = Color.blue;
        Gizmos.DrawRay(transform.position, transform.forward * 2f);
        
        // 绘制转向方向
        if (Mathf.Abs(m_CurrentSteerInput) > 0.1f)
        {
            Gizmos.color = Color.red;
            Vector3 steerDirection = Quaternion.AngleAxis(m_CurrentSteerAngle, Vector3.up) * transform.forward;
            Gizmos.DrawRay(transform.position + Vector3.up * 0.5f, steerDirection * 2f);
        }
        
        // 绘制角速度指示器
        if (m_AngularVelocity > 1f)
        {
            Gizmos.color = Color.yellow;
            float radius = Mathf.Clamp(m_AngularVelocity * 0.5f, 0.5f, 3f);
            Gizmos.DrawWireSphere(transform.position + Vector3.up * 2f, radius);
        }
    }

    #if UNITY_EDITOR
    [UnityEditor.CustomEditor(typeof(VehicleSteeringDebugger))]
    public class VehicleSteeringDebuggerEditor : UnityEditor.Editor
    {
        public override void OnInspectorGUI()
        {
            DrawDefaultInspector();
            
            GUILayout.Space(10);
            GUILayout.Label("调试说明:", UnityEditor.EditorStyles.boldLabel);
            GUILayout.Label("• 绿色射线: 车辆速度方向", UnityEditor.EditorStyles.miniLabel);
            GUILayout.Label("• 蓝色射线: 车辆朝向", UnityEditor.EditorStyles.miniLabel);
            GUILayout.Label("• 红色射线: 转向方向", UnityEditor.EditorStyles.miniLabel);
            GUILayout.Label("• 黄色圆圈: 角速度指示器", UnityEditor.EditorStyles.miniLabel);
            
            GUILayout.Space(10);
            if (GUILayout.Button("重置为推荐参数"))
            {
                VehicleSteeringDebugger debugger = (VehicleSteeringDebugger)target;
                debugger.debugSteerSmoothingSpeed = 12f;
                debugger.debugArcadeSteerBoost = 1.5f;
                debugger.debugSteerStabilityFactor = 0.3f;
            }
        }
    }
    #endif
}
