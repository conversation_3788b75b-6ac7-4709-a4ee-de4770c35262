using UnityEngine;
using UnityEditor;

/// <summary>
/// 编辑器工具：诊断和修复氮气系统问题
/// </summary>
public class NitroSystemDiagnostic : EditorWindow
{
    [MenuItem("Tools/Vehicle/Nitro System Diagnostic & Fix")]
    public static void ShowWindow()
    {
        GetWindow<NitroSystemDiagnostic>("Nitro System Diagnostic");
    }

    private bool enableNitroForAllVehicles = true;
    private float nitroConsumptionRate = 15f;
    private float nitroRegenerationRate = 20f;
    private float nitroRegenerationDelay = 2f;

    private void OnGUI()
    {
        GUILayout.Label("氮气系统诊断与修复工具", EditorStyles.boldLabel);
        GUILayout.Space(10);

        GUILayout.Label("当前问题诊断：", EditorStyles.boldLabel);
        GUILayout.Label("1. 氮气系统可能无限使用", EditorStyles.wordWrappedLabel);
        GUILayout.Label("2. 氮气音效重复播放", EditorStyles.wordWrappedLabel);
        
        GUILayout.Space(15);
        
        if (GUILayout.Button("诊断当前氮气系统状态", GUILayout.Height(25)))
        {
            DiagnoseNitroSystem();
        }
        
        GUILayout.Space(15);
        GUILayout.Label("修复选项", EditorStyles.boldLabel);
        
        enableNitroForAllVehicles = EditorGUILayout.Toggle("为所有车辆启用氮气系统", enableNitroForAllVehicles);
        
        GUILayout.Space(10);
        GUILayout.Label("氮气参数调整", EditorStyles.boldLabel);
        
        nitroConsumptionRate = EditorGUILayout.Slider("氮气消耗速率", nitroConsumptionRate, 5f, 50f);
        GUILayout.Label("每秒消耗的氮气量", EditorStyles.miniLabel);
        
        nitroRegenerationRate = EditorGUILayout.Slider("氮气恢复速率", nitroRegenerationRate, 5f, 40f);
        GUILayout.Label("每秒恢复的氮气量", EditorStyles.miniLabel);
        
        nitroRegenerationDelay = EditorGUILayout.Slider("氮气恢复延迟 (秒)", nitroRegenerationDelay, 0.5f, 5f);
        GUILayout.Label("停止使用氮气后多久开始恢复", EditorStyles.miniLabel);
        
        GUILayout.Space(20);

        if (GUILayout.Button("修复氮气系统问题", GUILayout.Height(30)))
        {
            FixNitroSystemIssues();
        }

        GUILayout.Space(10);

        if (GUILayout.Button("恢复默认氮气参数", GUILayout.Height(25)))
        {
            RestoreDefaultNitroParameters();
        }

        GUILayout.Space(15);
        GUILayout.Label("推荐设置", EditorStyles.boldLabel);
        if (GUILayout.Button("应用推荐的氮气设置"))
        {
            enableNitroForAllVehicles = true;
            nitroConsumptionRate = 15f;
            nitroRegenerationRate = 20f;
            nitroRegenerationDelay = 2f;
            FixNitroSystemIssues();
        }
        
        GUILayout.Space(10);
        GUILayout.Label("说明：", EditorStyles.boldLabel);
        GUILayout.Label("• 启用氮气系统：让所有车辆都能使用氮气", EditorStyles.miniLabel);
        GUILayout.Label("• 消耗速率：控制氮气使用的快慢", EditorStyles.miniLabel);
        GUILayout.Label("• 恢复速率：控制氮气恢复的快慢", EditorStyles.miniLabel);
        GUILayout.Label("• 恢复延迟：防止氮气立即恢复", EditorStyles.miniLabel);
    }

    private void DiagnoseNitroSystem()
    {
        Debug.Log("=== 氮气系统诊断开始 ===");
        
        // 检查VehicleData中的氮气设置
        string[] vehicleGuids = AssetDatabase.FindAssets("t:VehicleData");
        int enabledCount = 0;
        int disabledCount = 0;
        
        foreach (string guid in vehicleGuids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            VehicleData vehicleData = AssetDatabase.LoadAssetAtPath<VehicleData>(path);

            if (vehicleData != null)
            {
                if (vehicleData.m_EnableNitroSystemBaseline > 0)
                {
                    enabledCount++;
                    Debug.Log($"✓ {vehicleData.m_VehicleName}: 氮气系统已启用");
                }
                else
                {
                    disabledCount++;
                    Debug.Log($"✗ {vehicleData.m_VehicleName}: 氮气系统未启用 (可能导致无法使用氮气)");
                }
                
                // 检查氮气参数是否合理
                if (vehicleData.m_BaseNitroConsumptionRate < 5f)
                {
                    Debug.LogWarning($"⚠ {vehicleData.m_VehicleName}: 氮气消耗速率过低 ({vehicleData.m_BaseNitroConsumptionRate})，可能导致无限使用");
                }
                
                if (vehicleData.m_BaseNitroRegenerationRate > 30f)
                {
                    Debug.LogWarning($"⚠ {vehicleData.m_VehicleName}: 氮气恢复速率过高 ({vehicleData.m_BaseNitroRegenerationRate})，可能导致快速恢复");
                }
                
                if (vehicleData.m_BaseNitroRegenerationDelay < 1f)
                {
                    Debug.LogWarning($"⚠ {vehicleData.m_VehicleName}: 氮气恢复延迟过短 ({vehicleData.m_BaseNitroRegenerationDelay}s)，可能导致立即恢复");
                }
            }
        }
        
        // 检查场景中的CarController
        CarController[] carControllers = FindObjectsOfType<CarController>();
        Debug.Log($"场景中发现 {carControllers.Length} 个CarController组件");
        
        foreach (CarController carController in carControllers)
        {
            if (carController.currentVehicleData != null)
            {
                Debug.Log($"CarController '{carController.name}' 使用车辆数据: {carController.currentVehicleData.m_VehicleName}");
            }
            else
            {
                Debug.LogWarning($"⚠ CarController '{carController.name}' 没有分配车辆数据");
            }
        }
        
        Debug.Log($"=== 诊断结果 ===");
        Debug.Log($"启用氮气的车辆: {enabledCount}");
        Debug.Log($"未启用氮气的车辆: {disabledCount}");
        
        if (disabledCount > 0)
        {
            Debug.LogWarning("发现未启用氮气系统的车辆，这可能是氮气无法使用的原因！");
        }
        
        Debug.Log("=== 氮气系统诊断完成 ===");
        
        EditorUtility.DisplayDialog("诊断完成", 
            $"氮气系统诊断完成！\n\n" +
            $"启用氮气的车辆: {enabledCount}\n" +
            $"未启用氮气的车辆: {disabledCount}\n\n" +
            "详细信息请查看Console窗口。", "确定");
    }

    private void FixNitroSystemIssues()
    {
        string[] vehicleGuids = AssetDatabase.FindAssets("t:VehicleData");
        int fixedCount = 0;

        foreach (string guid in vehicleGuids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            VehicleData vehicleData = AssetDatabase.LoadAssetAtPath<VehicleData>(path);

            if (vehicleData != null)
            {
                // 记录原始值
                bool originalEnabled = vehicleData.m_EnableNitroSystemBaseline > 0;
                float originalConsumption = vehicleData.m_BaseNitroConsumptionRate;
                float originalRegen = vehicleData.m_BaseNitroRegenerationRate;
                float originalDelay = vehicleData.m_BaseNitroRegenerationDelay;
                
                // 修复氮气系统启用状态
                if (enableNitroForAllVehicles)
                {
                    vehicleData.m_EnableNitroSystemBaseline = 1;
                }
                
                // 修复氮气参数
                vehicleData.m_BaseNitroConsumptionRate = nitroConsumptionRate;
                vehicleData.m_BaseNitroRegenerationRate = nitroRegenerationRate;
                vehicleData.m_BaseNitroRegenerationDelay = nitroRegenerationDelay;
                
                // 确保氮气容量合理
                if (vehicleData.m_BaseMaxNitroCapacity < 50f)
                {
                    vehicleData.m_BaseMaxNitroCapacity = 100f;
                }
                
                // 确保氮气力量合理
                if (vehicleData.m_BaseNitroForceMagnitude < 5000f)
                {
                    vehicleData.m_BaseNitroForceMagnitude = 8000f;
                }
                
                // 标记为已修改
                EditorUtility.SetDirty(vehicleData);
                fixedCount++;
                
                Debug.Log($"修复车辆 '{vehicleData.m_VehicleName}':\n" +
                         $"  启用状态: {originalEnabled} → {vehicleData.m_EnableNitroSystemBaseline > 0}\n" +
                         $"  消耗速率: {originalConsumption:F1} → {vehicleData.m_BaseNitroConsumptionRate:F1}\n" +
                         $"  恢复速率: {originalRegen:F1} → {vehicleData.m_BaseNitroRegenerationRate:F1}\n" +
                         $"  恢复延迟: {originalDelay:F1}s → {vehicleData.m_BaseNitroRegenerationDelay:F1}s");
            }
        }

        // 保存所有修改
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        EditorUtility.DisplayDialog("修复完成", 
            $"氮气系统修复完成！\n\n" +
            $"修复了 {fixedCount} 个车辆的氮气系统。\n\n" +
            "主要改进：\n" +
            $"• 氮气系统启用: {enableNitroForAllVehicles}\n" +
            $"• 消耗速率: {nitroConsumptionRate:F1}/秒\n" +
            $"• 恢复速率: {nitroRegenerationRate:F1}/秒\n" +
            $"• 恢复延迟: {nitroRegenerationDelay:F1}秒\n\n" +
            "氮气音效重复播放问题也已修复！", "确定");
    }

    private void RestoreDefaultNitroParameters()
    {
        if (!EditorUtility.DisplayDialog("确认恢复", 
            "这将恢复所有车辆的默认氮气参数。\n\n确定要继续吗？", "确定", "取消"))
        {
            return;
        }

        string[] vehicleGuids = AssetDatabase.FindAssets("t:VehicleData");
        int restoredCount = 0;

        foreach (string guid in vehicleGuids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            VehicleData vehicleData = AssetDatabase.LoadAssetAtPath<VehicleData>(path);

            if (vehicleData != null)
            {
                // 恢复默认氮气参数
                vehicleData.m_EnableNitroSystemBaseline = 0; // 默认禁用
                vehicleData.m_BaseMaxNitroCapacity = 150f;
                vehicleData.m_BaseNitroConsumptionRate = 20f;
                vehicleData.m_BaseNitroForceMagnitude = 10000f;
                vehicleData.m_BaseNitroRegenerationRate = 25f;
                vehicleData.m_BaseNitroRegenerationDelay = 1f;
                
                EditorUtility.SetDirty(vehicleData);
                restoredCount++;
            }
        }

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        EditorUtility.DisplayDialog("恢复完成", 
            $"成功恢复了 {restoredCount} 个车辆的默认氮气参数。", "确定");
    }
}
