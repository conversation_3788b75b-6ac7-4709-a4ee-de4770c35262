using UnityEngine;
using UnityEngine.Audio;
using System.Collections.Generic;
using UnityEngine.SceneManagement;

/// <summary>
/// 统一的音频管理器，负责处理BGM和SFX
/// </summary>
public class AudioManager : MonoBehaviour
{
    #region 单例模式
    public static AudioManager Instance { get; private set; }
    private static bool s_IsInitialized = false;
    #endregion

    #region 序列化字段
    [Header("BGM设置")]
    [SerializeField, Toolt<PERSON>("BGM 音频混合器组")]
    private AudioMixerGroup m_BGMMixerGroup;
    [SerializeField, Tooltip("BGM 音量在混合器中暴露的参数名")]
    private string m_BGMVolumeParameterName = "BGMVolumeParam";
    [SerializeField, Tooltip("BGM 默认音量 (0-1)")]
    private float m_DefaultBGMVolume = 0.8f;

    [Header("SFX设置")]
    [SerializeField, Toolt<PERSON>("SFX 音频混合器组")]
    private AudioMixerGroup m_SFXMixerGroup;
    [SerializeField, Tooltip("SFX 音量在混合器中暴露的参数名")]
    private string m_SFXVolumeParameterName = "SFXVolumeParam";
    [SerializeField, Tooltip("SFX 默认音量 (0-1)")]
    private float m_DefaultSFXVolume = 0.8f;
    [SerializeField, Tooltip("SFX 音频源池大小")]
    private int m_SFXPoolSize = 5;
    [SerializeField, Tooltip("SFX 2D/3D混合 (0=2D, 1=3D)")]
    private float m_SFXSpatialBlend = 0f;

    [Header("车辆音效设置")]
    [SerializeField, Tooltip("车辆音效混合器组")]
    private AudioMixerGroup m_VehicleSFXMixerGroup;
    #endregion

    #region 私有字段
    private AudioSource m_BGMSource;
    private List<AudioSource> m_SFXPool = new List<AudioSource>();
    private Dictionary<string, AudioSource> m_VehicleAudioSources = new Dictionary<string, AudioSource>();

    private const string c_BgmVolumeKey = "BGMVolume";
    private const string c_SfxVolumeKey = "SFXVolume";
    #endregion

    #region Unity生命周期
    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            s_IsInitialized = true;

            InitializeBGMSystem();
            InitializeSFXPool();
            InitializeUISoundDictionary();

            // 加载音量设置
            LoadAndApplyBGMVolume();
            LoadAndApplySFXVolume();

            // 注册场景加载事件
            SceneManager.sceneLoaded += OnSceneLoaded;

            // 如果在场景中，自动注册UI元素
            if (Application.isPlaying)
            {
                RegisterAllUIElements();
            }

            Debug.Log("AudioManager: 实例已成功初始化", this);
        }
        else if (Instance != this)
        {
            Debug.LogWarning("AudioManager: 检测到重复的AudioManager实例，销毁当前实例", this);
            Destroy(gameObject);
        }
        else
        {
            Debug.Log("AudioManager: Awake被重复调用，但实例已经存在且是当前实例", this);

            // 即使是重复调用，也确保音量设置正确
            LoadAndApplyBGMVolume();
            LoadAndApplySFXVolume();
        }
    }

    private void OnDestroy()
    {
        if (Instance == this)
        {
            SceneManager.sceneLoaded -= OnSceneLoaded;
            s_IsInitialized = false;
        }
    }
    #endregion

    #region 初始化方法
    /// <summary>
    /// 初始化BGM系统
    /// </summary>
    private void InitializeBGMSystem()
    {
        m_BGMSource = GetComponent<AudioSource>();
        if (m_BGMSource == null)
        {
            m_BGMSource = gameObject.AddComponent<AudioSource>();
        }

        m_BGMSource.playOnAwake = false;
        m_BGMSource.loop = true;
        m_BGMSource.spatialBlend = 0f; // 2D音效

        if (m_BGMMixerGroup != null)
        {
            m_BGMSource.outputAudioMixerGroup = m_BGMMixerGroup;
        }
        else
        {
            Debug.LogWarning("AudioManager: BGM Mixer Group未设置，音量控制可能无法正常工作", this);
        }
    }

    /// <summary>
    /// 初始化SFX音频源池
    /// </summary>
    private void InitializeSFXPool()
    {
        // 清空现有的音频源池
        foreach (var audioSource in m_SFXPool)
        {
            if (audioSource != null)
            {
                Destroy(audioSource.gameObject);
            }
        }
        m_SFXPool.Clear();

        // 创建新的音频源池
        for (int i = 0; i < m_SFXPoolSize; i++)
        {
            GameObject audioSourceObj = new GameObject($"SFX_AudioSource_{i}");
            audioSourceObj.transform.SetParent(transform);
            AudioSource audioSource = audioSourceObj.AddComponent<AudioSource>();

            // 配置音频源
            audioSource.playOnAwake = false;
            audioSource.loop = false;
            audioSource.volume = m_DefaultSFXVolume;
            audioSource.spatialBlend = m_SFXSpatialBlend;

            if (m_SFXMixerGroup != null)
            {
                audioSource.outputAudioMixerGroup = m_SFXMixerGroup;
            }

            m_SFXPool.Add(audioSource);
        }
    }

    /// <summary>
    /// 场景加载事件处理
    /// </summary>
    private void OnSceneLoaded(Scene scene, LoadSceneMode mode)
    {
        // 在场景加载时重新应用音量设置
        LoadAndApplyBGMVolume();
        LoadAndApplySFXVolume();

        // 重新注册UI元素
        if (Application.isPlaying)
        {
            RegisterAllUIElements();
        }

        Debug.Log($"AudioManager: 场景 {scene.name} 已加载，已重新应用音频设置");
    }
    #endregion

    #region BGM方法
    /// <summary>
    /// 播放指定的背景音乐。
    /// </summary>
    /// <param name="musicClip">要播放的音乐片段。</param>
    /// <param name="loop">是否循环播放。</param>
    public void PlayMusic(AudioClip musicClip, bool loop = true)
    {
        if (m_BGMSource == null)
        {
            Debug.LogError("AudioManager: BGM AudioSource 为空，无法播放音乐。", this);
            return;
        }

        // 如果传入的 musicClip 为 null，则停止当前播放的音乐
        if (musicClip == null)
        {
            Debug.LogWarning("AudioManager: 尝试播放的 musicClip 为空。将停止当前BGM (如果有)。", this);
            if (m_BGMSource.isPlaying)
            {
                m_BGMSource.Stop();
            }
            m_BGMSource.clip = null; // 清除当前的音乐片段
            return;
        }

        // 如果要播放的音乐与当前播放的不同，或者当前没有音乐在播放，则播放新音乐
        if (m_BGMSource.clip != musicClip || !m_BGMSource.isPlaying)
        {
            // 如果当前有音乐在播放且要播放的音乐不同，先停止当前的
            if (m_BGMSource.isPlaying && m_BGMSource.clip != musicClip)
            {
                m_BGMSource.Stop();
            }
            m_BGMSource.clip = musicClip;
            m_BGMSource.loop = loop;

            // 确保应用正确的音量设置
            LoadAndApplyBGMVolume();

            m_BGMSource.Play();
            Debug.Log($"AudioManager: 开始播放BGM - {musicClip.name}", this);
        }
        // 如果要播放的音乐与当前正在播放的相同，则不执行任何操作 (保持继续播放)
        else
        {
            // 即使音乐相同，也确保音量设置正确
            LoadAndApplyBGMVolume();
        }
    }

    /// <summary>
    /// 停止当前播放的背景音乐。
    /// </summary>
    public void StopMusic()
    {
        if (m_BGMSource != null && m_BGMSource.isPlaying)
        {
            m_BGMSource.Stop();
            Debug.Log("AudioManager: BGM 已停止。", this);
        }
    }

    /// <summary>
    /// 设置 BGM 音量 (线性值 0-1)。
    /// </summary>
    /// <param name="linearVolume">音量值 (0 到 1)。</param>
    public void SetBGMVolume(float linearVolume)
    {
        if (m_BGMSource == null)
        {
            Debug.LogError("AudioManager.SetBGMVolume: m_BGMSource is null. Cannot set volume.", this);
            return;
        }
        if (m_BGMSource.outputAudioMixerGroup == null)
        {
            Debug.LogError("AudioManager.SetBGMVolume: m_BGMSource.outputAudioMixerGroup is null. Ensure it is assigned in the Inspector. Cannot set volume via mixer.", this);
            return;
        }

        AudioMixer mixer = m_BGMSource.outputAudioMixerGroup.audioMixer;
        if (mixer == null)
        {
            Debug.LogError($"AudioManager.SetBGMVolume: AudioMixer on group '{m_BGMSource.outputAudioMixerGroup.name}' is null. Cannot set volume via mixer. Ensure the AudioMixerGroup is correctly configured.", this);
            return;
        }

        float decibelVolume = ConvertLinearToDecibels(linearVolume);
        bool success = mixer.SetFloat(m_BGMVolumeParameterName, decibelVolume);

        if (success)
        {
            Debug.Log($"AudioManager: SetFloat for '{m_BGMVolumeParameterName}' to {decibelVolume}dB (linear: {linearVolume}) SUCCEEDED.", this);
            PlayerPrefs.SetFloat(c_BgmVolumeKey, linearVolume);
        }
        else
        {
            Debug.LogError($"AudioManager: SetFloat for '{m_BGMVolumeParameterName}' to {decibelVolume}dB (linear: {linearVolume}) FAILED. " +
                           $"Ensure the parameter name is correct and exposed in the AudioMixer, the mixer is active, and no snapshots are overriding this parameter immediately on startup.", this);
        }
    }
    #endregion

    #region SFX方法
    /// <summary>
    /// 播放一次性音效
    /// </summary>
    /// <param name="clip">音效片段</param>
    /// <param name="volume">音量 (0-1)</param>
    /// <param name="pitch">音调</param>
    /// <param name="position">3D音效的位置 (如果使用3D音效)</param>
    /// <returns>用于播放音效的AudioSource</returns>
    public AudioSource PlaySFX(AudioClip clip, float volume = 1.0f, float pitch = 1.0f, Vector3? position = null)
    {
        if (clip == null)
        {
            Debug.LogWarning("AudioManager: 尝试播放的SFX为空");
            return null;
        }

        AudioSource audioSource = GetAvailableSFXSource();
        if (audioSource == null)
        {
            Debug.LogWarning("AudioManager: 无法获取可用的SFX音频源");
            return null;
        }

        audioSource.clip = clip;
        audioSource.volume = volume;
        audioSource.pitch = pitch;
        audioSource.loop = false;

        if (position.HasValue)
        {
            audioSource.transform.position = position.Value;
            audioSource.spatialBlend = 1.0f; // 3D音效
        }
        else
        {
            audioSource.spatialBlend = m_SFXSpatialBlend;
        }

        audioSource.Play();
        return audioSource;
    }

    /// <summary>
    /// 设置SFX音量
    /// </summary>
    /// <param name="volume">音量值 (0-1)</param>
    public void SetSFXVolume(float volume)
    {
        if (m_SFXMixerGroup != null && m_SFXMixerGroup.audioMixer != null)
        {
            float decibelVolume = ConvertLinearToDecibels(volume);
            bool success = m_SFXMixerGroup.audioMixer.SetFloat(m_SFXVolumeParameterName, decibelVolume);

            if (success)
            {
                Debug.Log($"AudioManager: SetFloat for '{m_SFXVolumeParameterName}' to {decibelVolume}dB (linear: {volume}) SUCCEEDED.", this);
                PlayerPrefs.SetFloat(c_SfxVolumeKey, volume);
            }
            else
            {
                Debug.LogError($"AudioManager: SetFloat for '{m_SFXVolumeParameterName}' to {decibelVolume}dB (linear: {volume}) FAILED.", this);
            }
        }

        // 更新所有SFX音频源的音量
        foreach (var source in m_SFXPool)
        {
            if (source != null)
            {
                source.volume = volume;
            }
        }
    }

    /// <summary>
    /// 获取可用的SFX音频源
    /// </summary>
    /// <returns>可用的音频源</returns>
    private AudioSource GetAvailableSFXSource()
    {
        // 首先尝试找到一个未在播放的音频源
        foreach (var source in m_SFXPool)
        {
            if (source != null && !source.isPlaying)
            {
                return source;
            }
        }

        // 如果所有音频源都在播放，则使用第一个
        if (m_SFXPool.Count > 0)
        {
            return m_SFXPool[0];
        }

        return null;
    }
    #endregion

    #region 车辆音频方法
    /// <summary>
    /// 获取或创建车辆音频源
    /// </summary>
    /// <param name="vehicleId">车辆ID</param>
    /// <param name="audioType">音频类型 (如 "Engine", "Drift")</param>
    /// <param name="clip">音频片段 (来自VehicleData或PartData)</param>
    /// <param name="volume">音量</param>
    /// <param name="loop">是否循环播放</param>
    /// <param name="spatialBlend">2D/3D混合 (0=2D, 1=3D)</param>
    /// <param name="autoPlay">是否自动播放</param>
    /// <returns>车辆音频源</returns>
    public AudioSource GetVehicleAudioSource(string vehicleId, string audioType, AudioClip clip = null,
                                            float volume = 0.7f, bool loop = true, float spatialBlend = 1.0f,
                                            bool autoPlay = false)
    {
        string sourceKey = $"{vehicleId}_{audioType}";

        // 检查是否已经存在此音频源
        if (m_VehicleAudioSources.TryGetValue(sourceKey, out AudioSource existingSource) && existingSource != null)
        {
            // 如果音频片段发生变化，更新它
            if (clip != null && existingSource.clip != clip)
            {
                existingSource.clip = clip;

                // 如果需要自动播放且当前没有播放，则播放
                if (autoPlay && !existingSource.isPlaying)
                {
                    existingSource.Play();
                }
            }

            return existingSource;
        }

        // 创建新的音频源
        GameObject audioSourceObj = new GameObject($"Vehicle_{vehicleId}_{audioType}");
        audioSourceObj.transform.SetParent(transform);
        AudioSource newSource = audioSourceObj.AddComponent<AudioSource>();

        // 配置音频源
        newSource.playOnAwake = false;
        newSource.loop = loop;
        newSource.volume = volume;
        newSource.spatialBlend = spatialBlend;
        newSource.clip = clip;

        if (m_VehicleSFXMixerGroup != null)
        {
            newSource.outputAudioMixerGroup = m_VehicleSFXMixerGroup;
        }
        else if (m_SFXMixerGroup != null)
        {
            newSource.outputAudioMixerGroup = m_SFXMixerGroup;
        }

        // 添加到字典
        m_VehicleAudioSources[sourceKey] = newSource;

        // 如果需要自动播放且有音频片段
        if (autoPlay && clip != null)
        {
            newSource.Play();
        }

        return newSource;
    }

    /// <summary>
    /// 更新车辆引擎音频
    /// </summary>
    /// <param name="vehicleId">车辆ID</param>
    /// <param name="normalizedSpeed">归一化速度 (0-1)</param>
    /// <param name="motorInput">马达输入 (0-1)</param>
    /// <param name="pitchCurve">音调曲线</param>
    /// <param name="minPitch">最小音调</param>
    /// <param name="maxPitch">最大音调</param>
    /// <param name="inputPitchFactor">输入音调因子</param>
    public void UpdateVehicleEngineAudio(string vehicleId, float normalizedSpeed, float motorInput,
                                        AnimationCurve pitchCurve, float minPitch = 0.5f,
                                        float maxPitch = 2.0f, float inputPitchFactor = 0.2f)
    {
        string sourceKey = $"{vehicleId}_Engine";

        if (m_VehicleAudioSources.TryGetValue(sourceKey, out AudioSource engineSource) && engineSource != null)
        {
            if (engineSource.isPlaying)
            {
                float targetPitch = pitchCurve.Evaluate(normalizedSpeed);
                targetPitch += motorInput * inputPitchFactor;
                targetPitch = Mathf.Clamp(targetPitch, minPitch, maxPitch);
                engineSource.pitch = Mathf.Lerp(engineSource.pitch, targetPitch, Time.deltaTime * 5f);
            }
            else if (engineSource.clip != null)
            {
                engineSource.Play();
            }
        }
    }

    /// <summary>
    /// 更新车辆漂移音频
    /// </summary>
    /// <param name="vehicleId">车辆ID</param>
    /// <param name="shouldPlay">是否应该播放</param>
    public void UpdateVehicleDriftAudio(string vehicleId, bool shouldPlay)
    {
        string sourceKey = $"{vehicleId}_Drift";

        if (m_VehicleAudioSources.TryGetValue(sourceKey, out AudioSource driftSource) && driftSource != null)
        {
            if (shouldPlay)
            {
                if (!driftSource.isPlaying && driftSource.clip != null)
                {
                    driftSource.Play();
                }
            }
            else
            {
                if (driftSource.isPlaying)
                {
                    driftSource.Stop();
                }
            }
        }
    }

    /// <summary>
    /// 更新车辆氮气音频
    /// </summary>
    /// <param name="vehicleId">车辆ID</param>
    /// <param name="shouldPlay">是否应该播放</param>
    public void UpdateVehicleNitroAudio(string vehicleId, bool shouldPlay)
    {
        string sourceKey = $"{vehicleId}_Nitro";

        if (m_VehicleAudioSources.TryGetValue(sourceKey, out AudioSource nitroSource) && nitroSource != null)
        {
            if (shouldPlay)
            {
                if (!nitroSource.isPlaying && nitroSource.clip != null)
                {
                    nitroSource.Play();
                }
            }
            else
            {
                if (nitroSource.isPlaying)
                {
                    nitroSource.Stop();
                }
            }
        }
    }
    #endregion

    #region UI音效方法
    /// <summary>
    /// UI音效类型枚举
    /// </summary>
    public enum UISoundType
    {
        ButtonClick,        // 普通按钮点击
        MenuButtonClick,    // 菜单按钮点击
        SliderTick,         // 滑块调整时的段落音效
        ToggleOn,           // 开关打开
        ToggleOff,          // 开关关闭
        WindowOpen,         // 窗口打开
        WindowClose,        // 窗口关闭
        PurchaseSuccess,    // 购买成功
        PurchaseFail,       // 购买失败
        ItemSelect,         // 选择物品
        TabSwitch,          // 切换标签页
        Error,              // 错误提示
        Notification        // 通知提示
    }

    [Header("UI音效")]
    [SerializeField] private AudioClip m_ButtonClickSound;
    [SerializeField] private AudioClip m_MenuButtonClickSound;
    [SerializeField] private AudioClip m_SliderTickSound;
    [SerializeField] private AudioClip m_ToggleOnSound;
    [SerializeField] private AudioClip m_ToggleOffSound;
    [SerializeField] private AudioClip m_WindowOpenSound;
    [SerializeField] private AudioClip m_WindowCloseSound;
    [SerializeField] private AudioClip m_PurchaseSuccessSound;
    [SerializeField] private AudioClip m_PurchaseFailSound;
    [SerializeField] private AudioClip m_ItemSelectSound;
    [SerializeField] private AudioClip m_TabSwitchSound;
    [SerializeField] private AudioClip m_ErrorSound;
    [SerializeField] private AudioClip m_NotificationSound;

    private Dictionary<UISoundType, AudioClip> m_UISoundDictionary = new Dictionary<UISoundType, AudioClip>();
    private bool m_EnableUISounds = true;

    /// <summary>
    /// 初始化UI音效字典
    /// </summary>
    private void InitializeUISoundDictionary()
    {
        m_UISoundDictionary.Clear();
        m_UISoundDictionary.Add(UISoundType.ButtonClick, m_ButtonClickSound);
        m_UISoundDictionary.Add(UISoundType.MenuButtonClick, m_MenuButtonClickSound);
        m_UISoundDictionary.Add(UISoundType.SliderTick, m_SliderTickSound);
        m_UISoundDictionary.Add(UISoundType.ToggleOn, m_ToggleOnSound);
        m_UISoundDictionary.Add(UISoundType.ToggleOff, m_ToggleOffSound);
        m_UISoundDictionary.Add(UISoundType.WindowOpen, m_WindowOpenSound);
        m_UISoundDictionary.Add(UISoundType.WindowClose, m_WindowCloseSound);
        m_UISoundDictionary.Add(UISoundType.PurchaseSuccess, m_PurchaseSuccessSound);
        m_UISoundDictionary.Add(UISoundType.PurchaseFail, m_PurchaseFailSound);
        m_UISoundDictionary.Add(UISoundType.ItemSelect, m_ItemSelectSound);
        m_UISoundDictionary.Add(UISoundType.TabSwitch, m_TabSwitchSound);
        m_UISoundDictionary.Add(UISoundType.Error, m_ErrorSound);
        m_UISoundDictionary.Add(UISoundType.Notification, m_NotificationSound);
    }

    /// <summary>
    /// 播放UI音效
    /// </summary>
    /// <param name="soundType">音效类型</param>
    public void PlayUISound(UISoundType soundType)
    {
        if (!m_EnableUISounds) return;

        if (m_UISoundDictionary.TryGetValue(soundType, out AudioClip clip) && clip != null)
        {
            PlaySFX(clip);
        }
        else
        {
            Debug.LogWarning($"AudioManager: 未找到类型为 {soundType} 的UI音效或音效为空");
        }
    }

    /// <summary>
    /// 设置UI音效是否启用
    /// </summary>
    /// <param name="enable">是否启用</param>
    public void SetUISoundsEnabled(bool enable)
    {
        m_EnableUISounds = enable;
    }

    /// <summary>
    /// 注册所有UI元素的音效事件
    /// </summary>
    public void RegisterAllUIElements()
    {
        // 注册所有按钮
        RegisterAllButtons();

        // 注册所有滑块
        RegisterAllSliders();

        // 注册所有开关
        RegisterAllToggles();
    }

    /// <summary>
    /// 注册所有按钮的点击音效
    /// </summary>
    private void RegisterAllButtons()
    {
        UnityEngine.UI.Button[] buttons = FindObjectsOfType<UnityEngine.UI.Button>(true);
        foreach (UnityEngine.UI.Button button in buttons)
        {
            // 避免重复添加监听器
            button.onClick.RemoveListener(() => OnButtonClick(button));
            button.onClick.AddListener(() => OnButtonClick(button));
        }
    }

    /// <summary>
    /// 注册所有滑块的音效
    /// </summary>
    private void RegisterAllSliders()
    {
        UnityEngine.UI.Slider[] sliders = FindObjectsOfType<UnityEngine.UI.Slider>(true);
        foreach (UnityEngine.UI.Slider slider in sliders)
        {
            // 移除现有监听器以避免重复
            slider.onValueChanged.RemoveListener((value) => OnSliderValueChanged(slider, value));
            slider.onValueChanged.AddListener((value) => OnSliderValueChanged(slider, value));
        }
    }

    /// <summary>
    /// 注册所有开关的音效
    /// </summary>
    private void RegisterAllToggles()
    {
        UnityEngine.UI.Toggle[] toggles = FindObjectsOfType<UnityEngine.UI.Toggle>(true);
        foreach (UnityEngine.UI.Toggle toggle in toggles)
        {
            // 移除现有监听器以避免重复
            toggle.onValueChanged.RemoveListener((value) => OnToggleValueChanged(toggle, value));
            toggle.onValueChanged.AddListener((value) => OnToggleValueChanged(toggle, value));
        }
    }

    /// <summary>
    /// 按钮点击事件处理
    /// </summary>
    /// <param name="button">被点击的按钮</param>
    private void OnButtonClick(UnityEngine.UI.Button button)
    {
        // 根据按钮的名称或标签判断是否为菜单按钮
        string buttonName = button.name.ToLower();
        string buttonText = "";

        // 尝试获取按钮文本
        TMPro.TextMeshProUGUI tmpText = button.GetComponentInChildren<TMPro.TextMeshProUGUI>();
        if (tmpText != null)
        {
            buttonText = tmpText.text.ToLower();
        }
        else
        {
            UnityEngine.UI.Text legacyText = button.GetComponentInChildren<UnityEngine.UI.Text>();
            if (legacyText != null)
            {
                buttonText = legacyText.text.ToLower();
            }
        }

        // 判断是否为菜单按钮
        bool isMenuButton = buttonName.Contains("menu") ||
                           buttonText.Contains("menu") ||
                           buttonName.Contains("tab") ||
                           buttonText.Contains("tab") ||
                           buttonName.Contains("settings") ||
                           buttonText.Contains("settings");

        if (isMenuButton)
        {
            PlayUISound(UISoundType.MenuButtonClick);
        }
        else
        {
            PlayUISound(UISoundType.ButtonClick);
        }
    }

    /// <summary>
    /// 滑块值变化事件处理
    /// </summary>
    /// <param name="slider">滑块</param>
    /// <param name="value">新值</param>
    private void OnSliderValueChanged(UnityEngine.UI.Slider slider, float value)
    {
        // 检测滑块是否移动到了新的段落
        // 这里我们将滑块的值范围分成10个段落，每当值跨越段落边界时播放音效
        float range = slider.maxValue - slider.minValue;
        float segmentSize = range / 10f;

        int previousSegment = Mathf.FloorToInt((slider.value - slider.minValue) / segmentSize);
        int currentSegment = Mathf.FloorToInt((value - slider.minValue) / segmentSize);

        if (previousSegment != currentSegment)
        {
            PlayUISound(UISoundType.SliderTick);
        }
    }

    /// <summary>
    /// 开关值变化事件处理
    /// </summary>
    /// <param name="toggle">开关</param>
    /// <param name="value">新值</param>
    private void OnToggleValueChanged(UnityEngine.UI.Toggle toggle, bool value)
    {
        if (value)
        {
            PlayUISound(UISoundType.ToggleOn);
        }
        else
        {
            PlayUISound(UISoundType.ToggleOff);
        }
    }
    #endregion

    #region 辅助方法
    /// <summary>
    /// 加载并应用保存的BGM音量
    /// </summary>
    private void LoadAndApplyBGMVolume()
    {
        float savedVolume = PlayerPrefs.GetFloat(c_BgmVolumeKey, m_DefaultBGMVolume);
        Debug.Log($"AudioManager: 加载BGM音量设置: {savedVolume} (默认值: {m_DefaultBGMVolume})", this);
        SetBGMVolume(savedVolume);
    }

    /// <summary>
    /// 加载并应用保存的SFX音量
    /// </summary>
    private void LoadAndApplySFXVolume()
    {
        float savedVolume = PlayerPrefs.GetFloat(c_SfxVolumeKey, m_DefaultSFXVolume);
        Debug.Log($"AudioManager: 加载SFX音量设置: {savedVolume} (默认值: {m_DefaultSFXVolume})", this);
        SetSFXVolume(savedVolume);
    }

    /// <summary>
    /// 将线性音量值转换为分贝值
    /// </summary>
    /// <param name="linearValue">线性音量值 (0-1)</param>
    /// <returns>分贝值</returns>
    private float ConvertLinearToDecibels(float linearValue)
    {
        // 避免 log(0) 的情况
        // Mixer 的音量范围通常是 -80dB (静音) 到 0dB (最大声) 或 +20dB
        // 此处将线性值0映射到-80dB，线性值1映射到0dB。
        return Mathf.Log10(Mathf.Max(linearValue, 0.0001f)) * 20f;
    }
    #endregion
}